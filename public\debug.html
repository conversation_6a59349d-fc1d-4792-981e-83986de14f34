<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Debug - URL Click Test</title>
  <link rel="stylesheet" href="style.css">
  <style>
    .debug-container {
      background: #333;
      padding: 32px;
      border-radius: 8px;
      margin: 32px auto;
      max-width: 800px;
      font-family: monospace;
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    .success { background: #2d5a2d; color: #90ee90; }
    .error { background: #5a2d2d; color: #ff9090; }
    .info { background: #2d4a5a; color: #90d0ee; }
    .test-link {
      display: inline-block;
      margin: 5px;
      padding: 8px 12px;
      background: #444;
      color: #4af;
      text-decoration: none;
      border-radius: 4px;
      border: 1px solid #666;
    }
    .test-link:hover {
      background: #555;
    }
    #console-output {
      background: #222;
      border: 1px solid #666;
      padding: 10px;
      height: 200px;
      overflow-y: auto;
      font-size: 12px;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="debug-container">
    <h2>🔧 URL Click Debug Page</h2>
    
    <div id="status-container">
      <div class="status info">Loading JavaScript modules...</div>
    </div>
    
    <h3>Test Links</h3>
    <div>
      <a href="/" class="test-link">Home (Internal)</a>
      <a href="/lobby" class="test-link">Lobby (Internal)</a>
      <a href="/game" class="test-link">Game (Internal)</a>
      <a href="/nonexistent" class="test-link">404 Test (Internal)</a>
      <a href="https://github.com" class="test-link" target="_blank">GitHub (External)</a>
      <a href="https://google.com" class="test-link">Google (External - Auto Target)</a>
    </div>
    
    <h3>Console Output</h3>
    <div id="console-output"></div>
    
    <h3>Current State</h3>
    <div id="current-state">
      <p><strong>Current URL:</strong> <span id="current-url"></span></p>
      <p><strong>Router Status:</strong> <span id="router-status"></span></p>
    </div>
  </div>

  <script type="module">
    const statusContainer = document.getElementById('status-container');
    const consoleOutput = document.getElementById('console-output');
    const currentUrl = document.getElementById('current-url');
    const routerStatus = document.getElementById('router-status');
    
    function addStatus(message, type = 'info') {
      const div = document.createElement('div');
      div.className = `status ${type}`;
      div.textContent = message;
      statusContainer.appendChild(div);
    }
    
    function logToConsole(message) {
      consoleOutput.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
      consoleOutput.scrollTop = consoleOutput.scrollHeight;
    }
    
    function updateState() {
      currentUrl.textContent = window.location.href;
      routerStatus.textContent = 'Active';
    }
    
    // Override console methods to capture output
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;
    
    console.log = function(...args) {
      logToConsole('LOG: ' + args.join(' '));
      originalLog.apply(console, args);
    };
    
    console.warn = function(...args) {
      logToConsole('WARN: ' + args.join(' '));
      originalWarn.apply(console, args);
    };
    
    console.error = function(...args) {
      logToConsole('ERROR: ' + args.join(' '));
      originalError.apply(console, args);
    };
    
    // Test module loading
    try {
      import('../msec/framework/router.js').then(router => {
        addStatus('✅ Router module loaded successfully', 'success');
        logToConsole('Router module loaded');
        
        // Test router functions
        if (typeof router.navigate === 'function') {
          addStatus('✅ Router.navigate function available', 'success');
        } else {
          addStatus('❌ Router.navigate function missing', 'error');
        }
        
        if (typeof router.defineRoute === 'function') {
          addStatus('✅ Router.defineRoute function available', 'success');
        } else {
          addStatus('❌ Router.defineRoute function missing', 'error');
        }
        
        updateState();
        
        // Listen for navigation events
        window.addEventListener('popstate', () => {
          logToConsole('Navigation event detected');
          updateState();
        });
        
      }).catch(error => {
        addStatus('❌ Failed to load router module: ' + error.message, 'error');
        logToConsole('Router load error: ' + error.message);
      });
      
      import('../msec/framework/dom.js').then(dom => {
        addStatus('✅ DOM module loaded successfully', 'success');
        logToConsole('DOM module loaded');
      }).catch(error => {
        addStatus('❌ Failed to load DOM module: ' + error.message, 'error');
        logToConsole('DOM load error: ' + error.message);
      });
      
      import('../msec/framework/state.js').then(state => {
        addStatus('✅ State module loaded successfully', 'success');
        logToConsole('State module loaded');
      }).catch(error => {
        addStatus('❌ Failed to load state module: ' + error.message, 'error');
        logToConsole('State load error: ' + error.message);
      });
      
    } catch (error) {
      addStatus('❌ Critical error during module loading: ' + error.message, 'error');
      logToConsole('Critical error: ' + error.message);
    }
    
    // Update state initially
    updateState();
    
    // Log click events
    document.addEventListener('click', (e) => {
      if (e.target.tagName === 'A') {
        const href = e.target.getAttribute('href');
        logToConsole(`Link clicked: ${href}`);
        
        if (href.startsWith('http')) {
          logToConsole('External link - should open in new tab');
        } else if (href.startsWith('/')) {
          logToConsole('Internal link - should use SPA navigation');
        }
      }
    });
    
    logToConsole('Debug page initialized');
  </script>
</body>
</html>
