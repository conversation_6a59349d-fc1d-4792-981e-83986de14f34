// lobbyScreen.js
import { renderDOM } from '../../msec/framework/dom.js';
import { getState, setState } from '../../msec/framework/state.js';
import { navigate } from '../../msec/framework/router.js';
import { subscribeToState } from '../multiplayer/sync.js';

let lastPlayerList = '';
let lastCount = 0;
let lastPhase = '';
let lastCountdown = 0;

export function lobbyScreen() {
  const root = document.getElementById('app');
  let started = false;

  function renderLobby(players, count, countdown) {
    const vdom = {
      tag: 'div',
      attrs: { class: 'lobby-screen' },
      children: [
        { tag: 'h2', attrs: {}, children: ['Lobby'] },
        { tag: 'div', attrs: {}, children: [`Players: ${count}/4`] },
        {
          tag: 'ul',
          attrs: { class: 'player-list' },
          children: players.map(p => ({
            tag: 'li',
            attrs: {},
            children: [p.nickname],
          })),
        },
        { tag: 'div', attrs: { class: 'lobby-countdown' }, children: [`Game starts in: ${countdown}s`] },
        {
          tag: 'div',
          attrs: { class: 'links-demo', style: 'margin-top: 20px; font-size: 14px;' },
          children: [
            { tag: 'p', attrs: {}, children: ['Navigation test:'] },
            {
              tag: 'a',
              attrs: { href: '/', style: 'color: #4af; margin-right: 10px;' },
              children: ['Back to Nickname (Internal)']
            },
            {
              tag: 'a',
              attrs: { href: '/game', style: 'color: #4af; margin-right: 10px;' },
              children: ['Go to Game (Internal)']
            },
          ],
        },
      ],
    };
    renderDOM(vdom, root);
  }

  // Listen for state updates from server
  subscribeToState((state) => {
    const playerListStr = (state.players || []).map(p => p.id).join(',');
    const count = (state.players || []).length;
    const phase = state.phase;
    const cd = state.lobbyCountdown || 0;
    // Only skip re-render if player list, count, phase, and countdown are unchanged
    if (playerListStr === lastPlayerList && count === lastCount && phase === lastPhase && cd === lastCountdown) {
      return;
    }
    lastPlayerList = playerListStr;
    lastCount = count;
    lastPhase = phase;
    lastCountdown = cd;
    if (state.phase === 'game' && Array.isArray(state.map) && !started) {
      started = true;
      if (window.location.pathname !== '/game') {
        navigate('/game');
      }
      return;
    }
    renderLobby(state.players || [], count, cd);
    if (count === 4 && !started) {
      started = true;
      if (window.location.pathname !== '/game') {
        navigate('/game');
      }
    }
  });

  // Initial render
  const state = getState();
  renderLobby(state.players || [], (state.players || []).length, state.lobbyCountdown || 0);
}
