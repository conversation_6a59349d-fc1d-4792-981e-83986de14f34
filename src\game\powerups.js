export const POWERUP_TYPES = {
  BOMB_UP: 'bomb_up',
  FLAME_UP: 'flame_up',
  SPEED_UP: 'speed_up',
};

export class PowerUp {
  constructor(row, col, type) {
    this.row = row;
    this.col = col;
    this.type = type;
    this.collected = false;
  }

  applyToPlayer(player) {
    switch (this.type) {
      case POWERUP_TYPES.BOMB_UP:
        player.bombsCount += 1;
        break;
      case POWERUP_TYPES.FLAME_UP:
        player.flameRange += 1;
        break;
      case POWERUP_TYPES.SPEED_UP:
        player.speed += 1;
        break;
    }
  }
}
