// nicknameScreen.js
import { renderDOM } from '../../msec/framework/dom.js';
import { setState } from '../../msec/framework/state.js';
import { navigate } from '../../msec/framework/router.js';
import { send } from '../multiplayer/socket.js';

export function nicknameScreen() {
  const root = document.getElementById('app');
  const vdom = {
    tag: 'div',
    attrs: { class: 'nickname-screen' },
    children: [
      { tag: 'h2', attrs: {}, children: ['Enter your nickname'] },
      {
        tag: 'form',
        attrs: { id: 'nickname-form', autocomplete: 'off' },
        children: [
          {
            tag: 'input',
            attrs: {
              type: 'text',
              id: 'nickname-input',
              placeholder: 'Nickname',
              maxlength: 16,
              required: true,
              autofocus: true,
            },
            children: [],
          },
          {
            tag: 'button',
            attrs: { type: 'submit' },
            children: ['Join'],
          },
        ],
      },
    ],
  };
  renderDOM(vdom, root);

  document.getElementById('nickname-form').onsubmit = (e) => {
    e.preventDefault();
    const nickname = document.getElementById('nickname-input').value.trim();
    if (!nickname) return;
    setState({ nickname });
    send({ type: 'nickname', nickname });
    navigate('/lobby');
  };
}
