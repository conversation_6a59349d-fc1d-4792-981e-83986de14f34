// server/server.js

const WebSocket = require('ws');
const GameState = require('./gameState.js');
const { v4: uuidv4 } = require('uuid');

const PORT = 8080;
const wss = new WebSocket.Server({ port: PORT });
const game = new GameState();

// Map of playerId -> ws
const clients = new Map();
// Map of ws -> playerId
const wsToId = new Map();
const playerConnections = new Map(); // playerId -> Set of ws
const pendingRemovals = new Map(); // playerId -> timeoutId

let lastBroadcastPlayers = '';

console.log(`WebSocket server running on ws://localhost:${PORT}`);

wss.on('connection', (ws) => {
  let playerId = null;
  let nickname = null;

  ws.on('message', (data) => {
    let msg;
    try {
      msg = JSON.parse(data);
    } catch (e) {
      return;
    }
    // First message must be nickname (optionally with playerId)
    if (!playerId && msg.type === 'nickname') {
      nickname = (msg.nickname || '').trim().slice(0, 16);
      if (!nickname) return;
      // Cancel any pending removal for this player BEFORE addPlayer
      if (msg.playerId && pendingRemovals.has(msg.playerId)) {
        clearTimeout(pendingRemovals.get(msg.playerId));
        pendingRemovals.delete(msg.playerId);
      }
      // Try to reuse playerId if provided and not already taken
      let player = null;
      if (msg.playerId) {
        player = game.players.find(p => p.id === msg.playerId);
        if (player) {
          player.alive = true;
          player.nickname = nickname;
        }
      }
      if (!player) {
        player = game.addPlayer(nickname);
      }
      if (!player) {
        ws.send(JSON.stringify({ type: 'error', error: 'Game full' }));
        ws.close();
        return;
      }
      playerId = player.id;
      clients.set(playerId, ws);
      wsToId.set(ws, playerId);
      // Track this connection for the player
      if (!playerConnections.has(playerId)) playerConnections.set(playerId, new Set());
      playerConnections.get(playerId).add(ws);
      // Send playerId to client
      ws.send(JSON.stringify({ type: 'playerId', id: playerId }));
      return;
    }
    if (!playerId) return;
    // Handle actions
    if (msg.type === 'action') {
      game.handleAction(playerId, msg.action);
    } else if (msg.type === 'chat') {
      game.handleChat(playerId, msg.text);
    }
  });

  ws.on('close', () => {
    if (playerId) {
      // Remove this connection from the player's set
      const conns = playerConnections.get(playerId);
      if (conns) {
        conns.delete(ws);
        if (conns.size === 0) {
          // Delay removal to allow for quick reconnects
          const timeoutId = setTimeout(() => {
            game.removePlayer(playerId);
            playerConnections.delete(playerId);
            pendingRemovals.delete(playerId);
          }, 2000);
          pendingRemovals.set(playerId, timeoutId);
        }
      }
      clients.delete(playerId);
      wsToId.delete(ws);
    }
  });
});

// Game loop: update and broadcast state
setInterval(() => {
  game.update();
  const state = game.getState();
  if (state.phase === 'game') {
    // REMOVE or comment out this line to stop debug spam
    // console.log('[DEBUG] Server state in game phase:', {
    //   mapType: typeof state.map,
    //   mapIsArray: Array.isArray(state.map),
    //   mapLength: state.map && state.map.length,
    //   players: state.players.map(p => p.nickname)
    // });
  }
  const playerListStr = state.players.map(p => p.nickname).join(',');
  if (playerListStr !== lastBroadcastPlayers) {
    console.log('[Broadcast] Players:', playerListStr);
    lastBroadcastPlayers = playerListStr;
  }
  const msg = JSON.stringify({ type: 'state', state });
  for (const ws of wss.clients) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(msg);
    }
  }
}, 1000 / 30); // 30 FPS broadcast
