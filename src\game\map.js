const MAP_WIDTH = 17;
const MAP_HEIGHT = 15;
const TILE_SIZE = 32;

const SPAWN_ZONE_TILES = [
  [0, 0], [0, 1], [1, 0], [1, 1],
  [0, MAP_WIDTH - 1], [0, MAP_WIDTH - 2], [1, MAP_WIDTH - 1], [1, MAP_WIDTH - 2],
  [MAP_HEIGHT - 1, 0], [MAP_HEIGHT - 1, 1], [MAP_HEIGHT - 2, 0], [MAP_HEIGHT - 2, 1],
  [MAP_HEIGHT - 1, MAP_WIDTH - 1], [MAP_HEIGHT - 1, MAP_WIDTH - 2],
  [MAP_HEIGHT - 2, MAP_WIDTH - 1], [MAP_HEIGHT - 2, MAP_WIDTH - 2],
];

function isSpawnZone(row, col) {
  return SPAWN_ZONE_TILES.some(([r, c]) => r === row && c === col);
}

export function generateMap() {
  const map = [];
  for (let row = 0; row < MAP_HEIGHT; row++) {
    const currentRow = [];
    for (let col = 0; col < MAP_WIDTH; col++) {
      if (isSpawnZone(row, col)) {
        currentRow.push('E'); // Empty spawn zone
        continue;
      }
      if (row % 2 === 1 && col % 2 === 1) {
        currentRow.push('W'); // Wall
        continue;
      }
      currentRow.push(Math.random() < 0.6 ? 'B' : 'E'); // Block or Empty
    }
    map.push(currentRow);
  }
  return map;
}
