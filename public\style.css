body {
  font-family: Arial, sans-serif;
  background: #222;
  color: #fff;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

#app {
  margin-top: 32px;
}

.nickname-screen, .lobby-screen, .game-loading {
  background: #333;
  padding: 32px;
  border-radius: 8px;
  box-shadow: 0 2px 8px #000a;
  min-width: 320px;
  text-align: center;
}

.game-root {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.game-map {
  display: grid;
  grid-template-rows: repeat(15, 32px);
  grid-template-columns: repeat(17, 32px);
  background: #222;
  border: 4px solid #555;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}
.game-map-abs {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  display: block;
}
.player, .bomb, .explosion, .powerup {
  position: absolute;
  pointer-events: none;
  transform: translate(-50%, -50%);
}

.map-row {
  display: contents;
}

.map-tile {
  width: 32px;
  height: 32px;
  box-sizing: border-box;
  border: 1px solid #333;
  background: #222;
}
.tile-wall {
  background: #666;
}
.tile-block {
  background: #b97a56;
}
.tile-empty {
  background: #222;
}

.player {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #4af;
  border: 2px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
  font-size: 14px;
  z-index: 2;
}
.player-0 { background: #4af; }
.player-1 { background: #fa4; }
.player-2 { background: #4fa; }
.player-3 { background: #f44; }

.bomb {
  position: absolute;
  width: 24px;
  height: 24px;
  background: #222;
  border: 3px solid #000;
  border-radius: 50%;
  z-index: 3;
  left: 4px;
  top: 4px;
}

.explosion {
  position: absolute;
  width: 32px;
  height: 32px;
  background: radial-gradient(circle, #ff0 60%, #f80 100%);
  opacity: 0.8;
  z-index: 4;
}

.powerup {
  position: absolute;
  width: 20px;
  height: 20px;
  left: 6px;
  top: 6px;
  border-radius: 4px;
  z-index: 2;
  background: #fff;
  border: 2px solid #0cf;
}
.powerup-bomb_up { background: #0cf; }
.powerup-flame_up { background: #f80; }
.powerup-speed_up { background: #0f8; }

/* --- Modern Chat Lobby Styles --- */
.chat-container {
  background: rgba(34, 34, 34, 0.85);
  border: 1.5px solid #3af;
  border-radius: 14px;
  width: 544px;
  max-width: 95vw;
  padding: 0 0 8px 0;
  position: fixed;
  left: 50%;
  bottom: 36px;
  transform: translateX(-50%);
  z-index: 100;
  box-shadow: 0 8px 32px 0 rgba(0,0,0,0.35), 0 1.5px 8px #3af2;
  backdrop-filter: blur(4px);
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.chat-header {
  padding: 10px 20px 6px 20px;
  font-size: 17px;
  font-weight: bold;
  color: #3af;
  letter-spacing: 1px;
  border-bottom: 1px solid #3af4;
  text-align: left;
  background: transparent;
  border-radius: 14px 14px 0 0;
}
.chat-messages {
  height: 140px;
  overflow-y: auto;
  background: transparent;
  border-radius: 0 0 8px 8px;
  padding: 10px 18px 4px 18px;
  margin-bottom: 0;
  font-size: 15px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.chat-message {
  background: rgba(60, 60, 80, 0.7);
  border-radius: 8px;
  padding: 4px 10px;
  margin-bottom: 0;
  color: #fff;
  word-break: break-word;
  box-shadow: 0 1px 3px #0002;
  font-size: 15px;
  max-width: 90%;
  align-self: flex-start;
}
.chat-message.me {
  background: rgba(58, 170, 255, 0.18);
  color: #3af;
  align-self: flex-end;
}
.chat-form {
  display: flex;
  gap: 8px;
  padding: 8px 16px 0 16px;
  align-items: center;
}
.chat-form input {
  flex: 1;
  padding: 7px 12px;
  border-radius: 6px;
  border: 1.5px solid #3af4;
  background: #181c22;
  color: #fff;
  font-size: 15px;
  outline: none;
  transition: border 0.2s;
}
.chat-form input:focus {
  border: 1.5px solid #3af;
  background: #222a33;
}
.chat-form button {
  background: linear-gradient(90deg, #3af 60%, #4af 100%);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 7px 18px;
  font-size: 15px;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 1px 4px #3af2;
  transition: background 0.2s, box-shadow 0.2s;
}
.chat-form button:hover {
  background: linear-gradient(90deg, #4af 60%, #3af 100%);
  box-shadow: 0 2px 8px #3af3;
}

.lobby-countdown {
  font-size: 18px;
  margin-top: 12px;
  color: #ff0;
}

.player-list {
  list-style: none;
  padding: 0;
  margin: 8px 0 0 0;
}
.player-list li {
  margin: 2px 0;
}
.player-nick {
  display: block;
  font-size: 13px;
  font-weight: bold;
  text-shadow: 0 1px 2px #000a;
}
.player-lives {
  display: block;
  font-size: 15px;
  color: #f44;
  letter-spacing: 2px;
  margin-top: 1px;
  text-shadow: 0 1px 2px #000a;
}
.player-dead {
  opacity: 0.4;
  filter: grayscale(0.7);
}
