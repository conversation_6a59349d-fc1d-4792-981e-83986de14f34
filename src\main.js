// main.js
import { defineRoute, renderRoute } from '../msec/framework/router.js';
import { nicknameScreen } from './ui/nicknameScreen.js';
import { lobbyScreen } from './ui/lobbyScreen.js';
import { gameScreen } from './ui/gameScreen.js';
import { connect } from './multiplayer/socket.js';
import { setState } from '../msec/framework/state.js';

// Connect to WebSocket server (adjust URL as needed)
connect('ws://localhost:8080');

// Define routes
defineRoute('/', nicknameScreen);
defineRoute('/lobby', lobbyScreen);
defineRoute('/game', gameScreen);

// Initial render
window.onload = () => {
  // Ensure there is a root element
  if (!document.getElementById('app')) {
    const appDiv = document.createElement('div');
    appDiv.id = 'app';
    document.body.appendChild(appDiv);
  }
  renderRoute();
};



