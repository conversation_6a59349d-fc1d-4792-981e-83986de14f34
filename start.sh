#!/bin/bash

# Start the WebSocket server (Node.js backend)
echo "Starting Bomberman WebSocket server on port 8080..."
node server/server.js &
WS_PID=$!

# Check for npx and install serve if missing
if ! command -v npx &> /dev/null; then
  echo "npx not found. Please install Node.js and npm."
  kill $WS_PID
  exit 1
fi

# Serve the public directory on port 3000
echo "Serving frontend from ./public at http://localhost:3000 ..."
npx serve public -l 3000 &
SERVE_PID=$!

# Trap Ctrl+C to kill both processes
trap "echo 'Stopping servers...'; kill $WS_PID $SERVE_PID" SIGINT

# Wait for both processes
wait $WS_PID $SERVE_PID 