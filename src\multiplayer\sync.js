// sync.js
// Handles game state sync via WebSocket
import { onMessage, send } from './socket.js';

let stateListeners = [];

export function subscribeToState(fn) {
  stateListeners.push(fn);
}

// Call this once to start listening for state updates from server
onMessage((msg) => {
  if (msg.type === 'state') {
    stateListeners.forEach(fn => fn(msg.state));
  }
});

// Send player action to server
export function sendAction(action) {
  send({ type: 'action', action });
}

// Send chat message to server
export function sendChat(text) {
  send({ type: 'chat', text });
}
