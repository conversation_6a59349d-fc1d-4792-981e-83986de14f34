// bomb.js

export class Bomb {
  constructor(row, col, ownerId, flameRange, onExplodeCallback) {
    this.row = row;               // bomb position row
    this.col = col;               // bomb position col
    this.ownerId = ownerId;       // player who placed the bomb
    this.flameRange = flameRange; // explosion range
    this.timer = 3000;            // countdown in ms (3 seconds)
    this.exploded = false;
    this.onExplodeCallback = onExplodeCallback; // called when bomb explodes
    this.startTime = Date.now();
  }

  update() {
    if (this.exploded) return;

    const elapsed = Date.now() - this.startTime;
    if (elapsed >= this.timer) {
      this.exploded = true;
      if (this.onExplodeCallback) {
        this.onExplodeCallback(this);
      }
    }
  }
}
