// gameScreen.js
import { renderGame } from '../game/renderer.js';
import { subscribeToState, sendAction } from '../multiplayer/sync.js';
import { getState } from '../../msec/framework/state.js';
import { chatUI } from './chat.js';

let root = null;

export function gameScreen() {
  root = document.getElementById('app');

  // Clear root and create static containers ONCE
  root.innerHTML = '';
  const gameRoot = document.createElement('div');
  gameRoot.id = 'game-root';
  root.appendChild(gameRoot);

  const chatRoot = document.createElement('div');
  chatRoot.id = 'chat-root';
  root.appendChild(chatRoot);

  // Create chat UI ONCE
  chatUI(chatRoot);

  function render(state) {
    // Only update gameRoot, not chatRoot
    if (!Array.isArray(state.map)) {
      gameRoot.innerHTML = '<div class="game-loading">Waiting for game to start...</div>';
      return;
    }
    // Clear and re-render game only
    gameRoot.innerHTML = '';
    renderGame({
      map: state.map,
      players: state.players,
      bombs: state.bombs,
      explosions: state.explosions || [],
      powerups: state.powerups || [],
    }, gameRoot);
  }

  // Subscribe to state updates
  subscribeToState(render);

  // Initial render
  render(getState());

  // Handle player input
  window.onkeydown = (e) => {
    if (document.activeElement.tagName === 'INPUT') return; // Don't move if typing in chat
    let action = null;
    switch (e.key) {
      case 'ArrowUp': case 'w': case 'W': action = { type: 'move', dir: 'up' }; break;
      case 'ArrowDown': case 's': case 'S': action = { type: 'move', dir: 'down' }; break;
      case 'ArrowLeft': case 'a': case 'A': action = { type: 'move', dir: 'left' }; break;
      case 'ArrowRight': case 'd': case 'D': action = { type: 'move', dir: 'right' }; break;
      case ' ': action = { type: 'bomb' }; break;
    }
    if (action) {
      sendAction(action);
      e.preventDefault();
    }
  };
}
