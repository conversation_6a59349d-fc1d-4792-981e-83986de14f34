// renderer.js
import { renderDOM } from '../../msec/framework/dom.js';

// Tile types
const TILE_CLASSES = {
  'E': 'tile-empty',
  'W': 'tile-wall',
  'B': 'tile-block',
};

function renderMap(map) {
  if (!Array.isArray(map)) {
    return { tag: 'div', attrs: { class: 'game-map-empty' }, children: [] };
  }
  return {
    tag: 'div',
    attrs: { class: 'game-map' },
    children: [
      ...map.map((row, rowIdx) => ({
        tag: 'div',
        attrs: { class: 'map-row' },
        children: row.map((cell, colIdx) => ({
          tag: 'div',
          attrs: {
            class: `map-tile ${TILE_CLASSES[cell] || ''}`,
            'data-row': rowIdx,
            'data-col': colIdx,
          },
          children: [],
        })),
      })),
      {
        tag: 'div',
        attrs: { class: 'game-map-abs' },
        children: [] // will be filled in renderGame
      }
    ],
  };
}

function renderPlayers(players) {
  return players.map(player => ({
    tag: 'div',
    attrs: {
      class: `player player-${player.id}${player.alive ? '' : ' player-dead'}`,
      style: `left: ${player.col * 32 + 16}px; top: ${player.row * 32 + 16}px;`,
      'data-nickname': player.nickname,
    },
    children: [
      {
        tag: 'span',
        attrs: { class: 'player-nick' },
        children: [player.nickname]
      },
      {
        tag: 'span',
        attrs: { class: 'player-lives' },
        children: Array(player.lives).fill('❤')
      }
    ],
  }));
}

function renderBombs(bombs) {
  return bombs.filter(b => !b.exploded).map(bomb => ({
    tag: 'div',
    attrs: {
      class: 'bomb',
      style: `left: ${bomb.col * 32 + 16}px; top: ${bomb.row * 32 + 16}px;`,
    },
    children: [],
  }));
}

function renderExplosions(explosions) {
  // explosions: array of {row, col}
  return explosions.map(ex => ({
    tag: 'div',
    attrs: {
      class: 'explosion',
      style: `left: ${ex.col * 32 + 16}px; top: ${ex.row * 32 + 16}px;`,
    },
    children: [],
  }));
}

function renderPowerups(powerups) {
  return powerups.filter(p => !p.collected).map(powerup => ({
    tag: 'div',
    attrs: {
      class: `powerup powerup-${powerup.type}`,
      style: `left: ${powerup.col * 32 + 16}px; top: ${powerup.row * 32 + 16}px;`,
    },
    children: [],
  }));
}

export function renderGame({ map, players, bombs, explosions, powerups }, root) {
  // Compose all game elements into a single virtual DOM tree
  const mapVdom = renderMap(map);
  // Find the .game-map-abs container and fill it with absolute elements
  if (mapVdom.children && mapVdom.children.length > 0) {
    const absLayer = mapVdom.children.find(child => child.attrs && child.attrs.class === 'game-map-abs');
    if (absLayer) {
      absLayer.children = [
        ...renderBombs(bombs),
        ...renderExplosions(explosions || []),
        ...renderPowerups(powerups || []),
        ...renderPlayers(players),
      ];
    }
  }
  const vdom = {
    tag: 'div',
    attrs: { class: 'game-root' },
    children: [mapVdom],
  };
  renderDOM(vdom, root);
}
