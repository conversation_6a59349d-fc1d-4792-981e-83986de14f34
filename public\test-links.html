<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>URL Click Test - Bomberman DOM</title>
  <link rel="stylesheet" href="style.css">
  <style>
    .test-container {
      background: #333;
      padding: 32px;
      border-radius: 8px;
      box-shadow: 0 2px 8px #000a;
      min-width: 320px;
      text-align: center;
      margin: 32px auto;
      max-width: 600px;
    }
    .test-links {
      margin: 20px 0;
    }
    .test-links a {
      display: block;
      margin: 10px 0;
      padding: 10px;
      background: #444;
      color: #4af;
      text-decoration: none;
      border-radius: 4px;
      transition: background 0.2s;
    }
    .test-links a:hover {
      background: #555;
    }
    .external {
      background: #543 !important;
    }
    .external:hover {
      background: #654 !important;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h2>URL Click Test Page</h2>
    <p>This page tests if URLs are opening properly when clicked.</p>
    
    <div class="test-links">
      <h3>Internal Links (SPA Navigation)</h3>
      <a href="/">Go to Nickname Screen</a>
      <a href="/lobby">Go to Lobby Screen</a>
      <a href="/game">Go to Game Screen</a>
      <a href="/nonexistent">Test 404 Redirect</a>
      
      <h3>External Links (New Tab)</h3>
      <a href="https://github.com" class="external">GitHub</a>
      <a href="https://developer.mozilla.org" class="external">MDN Web Docs</a>
      <a href="https://stackoverflow.com" class="external">Stack Overflow</a>
      <a href="https://google.com" class="external">Google</a>
    </div>
    
    <div style="margin-top: 30px;">
      <h3>Test Instructions</h3>
      <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
        <li><strong>Internal links</strong> should navigate within the app without page reload</li>
        <li><strong>External links</strong> should open in new tabs</li>
        <li>Browser back/forward buttons should work</li>
        <li>URL bar should update for internal navigation</li>
      </ul>
    </div>
  </div>

  <script type="module" src="/src/main.js"></script>
</body>
</html>
