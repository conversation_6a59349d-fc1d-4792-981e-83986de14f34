// engine.js

import { generateMap } from './map.js';
import { Player } from './player.js';
import { Bomb } from './bomb.js';
import { PowerUp, POWERUP_TYPES } from './powerups.js';

function randomPowerupType() {
  const types = [POWERUP_TYPES.BOMB_UP, POWERUP_TYPES.FLAME_UP, POWERUP_TYPES.SPEED_UP];
  return types[Math.floor(Math.random() * types.length)];
}

export class Engine {
  constructor() {
    this.map = generateMap();
    this.players = [];
    this.bombs = [];
    this.explosions = []; // {row, col, expiresAt}
    this.powerups = [];
    this.lastFrameTime = 0;
    this.fpsInterval = 1000 / 60; // target 60 FPS
    this.running = false;
    this.winner = null;
  }

  addPlayer(id, nickname, startRow, startCol) {
    const player = new Player(id, nickname, startRow, startCol);
    this.players.push(player);
  }

  placeBomb(player) {
    if (!player.alive) return;
    const activeBombs = this.bombs.filter(bomb => bomb.ownerId === player.id && !bomb.exploded);
    if (activeBombs.length >= player.bombsCount) return;
    // Prevent multiple bombs on same tile
    if (this.bombs.some(b => b.row === player.row && b.col === player.col && !b.exploded)) return;
    const bomb = new Bomb(player.row, player.col, player.id, player.flameRange, this.handleExplosion.bind(this));
    this.bombs.push(bomb);
  }

  handleExplosion(bomb) {
    // Calculate explosion area (cross pattern)
    const affected = [{ row: bomb.row, col: bomb.col }];
    const directions = [
      { dr: -1, dc: 0 }, // up
      { dr: 1, dc: 0 },  // down
      { dr: 0, dc: -1 }, // left
      { dr: 0, dc: 1 },  // right
    ];
    for (const { dr, dc } of directions) {
      for (let i = 1; i <= bomb.flameRange; i++) {
        const r = bomb.row + dr * i;
        const c = bomb.col + dc * i;
        if (r < 0 || r >= this.map.length || c < 0 || c >= this.map[0].length) break;
        if (this.map[r][c] === 'W') break; // Wall blocks flames
        affected.push({ row: r, col: c });
        if (this.map[r][c] === 'B') break; // Block stops flames
      }
    }
    // Mark explosion for rendering (lasts 500ms)
    const now = Date.now();
    affected.forEach(({ row, col }) => {
      this.explosions.push({ row, col, expiresAt: now + 500 });
    });
    // Destroy blocks, maybe spawn powerups
    affected.forEach(({ row, col }) => {
      if (this.map[row][col] === 'B') {
        this.map[row][col] = 'E';
        if (Math.random() < 0.3) { // 30% chance
          this.powerups.push(new PowerUp(row, col, randomPowerupType()));
        }
      }
    });
    // Damage players
    this.players.forEach(player => {
      if (!player.alive) return;
      if (affected.some(a => a.row === player.row && a.col === player.col)) {
        player.loseLife();
      }
    });
    // Chain reaction: trigger other bombs in explosion
    this.bombs.forEach(b => {
      if (!b.exploded && affected.some(a => a.row === b.row && a.col === b.col)) {
        b.exploded = true;
        this.handleExplosion(b);
      }
    });
    // Mark bomb as exploded
    bomb.exploded = true;
  }

  update(currentTime) {
    if (!this.running) return;
    if (!this.lastFrameTime) this.lastFrameTime = currentTime;
    const elapsed = currentTime - this.lastFrameTime;
    if (elapsed > this.fpsInterval) {
      this.lastFrameTime = currentTime - (elapsed % this.fpsInterval);
      // Update bombs
      this.bombs.forEach(bomb => bomb.update());
      // Remove expired explosions
      const now = Date.now();
      this.explosions = this.explosions.filter(ex => ex.expiresAt > now);
      // Powerup pickup
      this.players.forEach(player => {
        if (!player.alive) return;
        this.powerups.forEach(powerup => {
          if (!powerup.collected && powerup.row === player.row && powerup.col === player.col) {
            powerup.applyToPlayer(player);
            powerup.collected = true;
          }
        });
      });
      // Win condition
      const alive = this.players.filter(p => p.alive);
      if (alive.length === 1 && !this.winner) {
        this.winner = alive[0].nickname;
        this.running = false;
      }
    }
    requestAnimationFrame(this.update.bind(this));
  }

  start() {
    this.running = true;
    this.winner = null;
    requestAnimationFrame(this.update.bind(this));
  }

  stop() {
    this.running = false;
  }
}
