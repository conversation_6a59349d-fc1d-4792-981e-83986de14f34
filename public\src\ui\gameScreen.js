// gameScreen.js
import { renderDOM } from '../../msec/framework/dom.js';
import { subscribeToState, sendAction } from '../multiplayer/sync.js';
import { getState } from '../../msec/framework/state.js';
import { navigate } from '../../msec/framework/router.js';

let root = null;

export function gameScreen() {
  root = document.getElementById('app');

  function render(state) {
    const vdom = {
      tag: 'div',
      attrs: { class: 'game-screen' },
      children: [
        { tag: 'h2', attrs: {}, children: ['Game Screen'] },
        { tag: 'div', attrs: {}, children: ['Game functionality would be here...'] },
        {
          tag: 'div',
          attrs: { class: 'links-demo', style: 'margin-top: 20px; font-size: 14px;' },
          children: [
            { tag: 'p', attrs: {}, children: ['Navigation test:'] },
            {
              tag: 'a',
              attrs: { href: '/', style: 'color: #4af; margin-right: 10px;' },
              children: ['Back to Nickname (Internal)']
            },
            {
              tag: 'a',
              attrs: { href: '/lobby', style: 'color: #4af; margin-right: 10px;' },
              children: ['Back to Lobby (Internal)']
            },
            {
              tag: 'a',
              attrs: { href: 'https://stackoverflow.com', style: 'color: #4af;' },
              children: ['Stack Overflow (External)']
            },
          ],
        },
      ],
    };
    renderDOM(vdom, root);
  }

  // Subscribe to state updates
  subscribeToState(render);

  // Initial render
  render(getState());
}
