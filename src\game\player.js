// player.js

export class Player {
  constructor(id, nickname, startRow, startCol) {
    this.id = id;                   // unique player id
    this.nickname = nickname;       // player nickname
    this.row = startRow;            // current grid row
    this.col = startCol;            // current grid column
    this.lives = 3;                 // start with 3 lives
    this.speed = 1;                 // movement speed (tiles per tick or similar)
    this.bombsCount = 1;            // bombs allowed at a time
    this.flameRange = 1;            // explosion range
    this.alive = true;              // is player alive?
  }

  move(direction, map) {
    if (!this.alive) return;

    // Calculate new position based on direction
    let newRow = this.row;
    let newCol = this.col;

    switch(direction) {
      case 'up': newRow -= this.speed; break;
      case 'down': newRow += this.speed; break;
      case 'left': newCol -= this.speed; break;
      case 'right': newCol += this.speed; break;
    }

    // Check map boundaries
    if (newRow < 0 || newRow >= map.length || newCol < 0 || newCol >= map[0].length) {
      return; // outside map, ignore
    }

    // Check if new tile is walkable ('E' for empty)
    if (map[newRow][newCol] === 'E') {
      this.row = newRow;
      this.col = newCol;
    }
  }

  loseLife() {
    this.lives--;
    if (this.lives <= 0) {
      this.alive = false;
    }
  }

  addPowerup(type) {
    switch(type) {
      case 'bomb':
        this.bombsCount++;
        break;
      case 'flame':
        this.flameRange++;
        break;
      case 'speed':
        this.speed++;  // consider max speed limit if needed
        break;
    }
  }
}
