// server/gameState.js

const { v4: uuidv4 } = require('uuid');

const MAP_WIDTH = 17;
const MAP_HEIGHT = 15;
const TILE_SIZE = 32;
const SPAWN_POSITIONS = [
  { row: 0, col: 0 },
  { row: 0, col: MAP_WIDTH - 1 },
  { row: MAP_HEIGHT - 1, col: 0 },
  { row: MAP_HEIGHT - 1, col: MAP_WIDTH - 1 },
];

const POWERUP_TYPES = ['bomb_up', 'flame_up', 'speed_up'];

function randomPowerupType() {
  return POWERUP_TYPES[Math.floor(Math.random() * POWERUP_TYPES.length)];
}

function generateMap() {
  // Similar to client
  const map = [];
  for (let row = 0; row < MAP_HEIGHT; row++) {
    const currentRow = [];
    for (let col = 0; col < MAP_WIDTH; col++) {
      // Always empty for spawn corners and their adjacent tiles
      if (
        (row < 2 && col < 2) ||
        (row < 2 && col > MAP_WIDTH - 3) ||
        (row > MAP_HEIGHT - 3 && col < 2) ||
        (row > MAP_HEIGHT - 3 && col > MAP_WIDTH - 3) ||
        (row === 2 && (col === 0 || col === MAP_WIDTH - 1)) ||
        (row === 0 && (col === 2 || col === MAP_WIDTH - 3)) ||
        (row === MAP_HEIGHT - 3 && (col === 0 || col === MAP_WIDTH - 1)) ||
        (row === MAP_HEIGHT - 1 && (col === 2 || col === MAP_WIDTH - 3))
      ) {
        currentRow.push('E');
        continue;
      }
      if (row % 2 === 1 && col % 2 === 1) {
        currentRow.push('W');
        continue;
      }
      currentRow.push(Math.random() < 0.6 ? 'B' : 'E');
    }
    map.push(currentRow);
  }
  return map;
}

class Player {
  constructor(id, nickname, startRow, startCol) {
    this.id = id;
    this.nickname = nickname;
    this.row = startRow;
    this.col = startCol;
    this.lives = 3;
    this.speed = 1;
    this.bombsCount = 1;
    this.flameRange = 1;
    this.alive = true;
  }
}

class Bomb {
  constructor(row, col, ownerId, flameRange) {
    this.row = row;
    this.col = col;
    this.ownerId = ownerId;
    this.flameRange = flameRange;
    this.timer = 3000;
    this.exploded = false;
    this.startTime = Date.now();
  }
}

class PowerUp {
  constructor(row, col, type) {
    this.row = row;
    this.col = col;
    this.type = type;
    this.collected = false;
  }
}

class GameState {
  constructor() {
    this.map = generateMap();
    this.players = [];
    this.bombs = [];
    this.explosions = [];
    this.powerups = [];
    this.chat = [];
    this.phase = 'lobby';
    this.winner = null;
    this.lastUpdate = Date.now();
    this.countdown = 10;
    this.lobbyStart = Date.now();
    this._lastPlayerCount = undefined; // Initialize for tracking player count changes
  }

  addPlayer(nickname) {
    console.log(`[GameState] addPlayer called with nickname: ${nickname}`);
    if (this.players.length >= 4) return null;
    // Find the first available spawn position
    const usedPositions = this.players.map(p => `${p.row},${p.col}`);
    console.log(`[GameState] usedPositions:`, usedPositions);
    let pos = null;
    for (const candidate of SPAWN_POSITIONS) {
      if (!usedPositions.includes(`${candidate.row},${candidate.col}`)) {
        pos = candidate;
        break;
      }
    }
    if (!pos) {
      console.error('[GameState] No available spawn position for new player!');
      return null;
    }
    const id = uuidv4();
    const player = new Player(id, nickname, pos.row, pos.col);
    this.players.push(player);
    console.log(`[GameState] Player added: ${nickname} (${id}) at (${pos.row},${pos.col})`);
    console.log(`[GameState] Current players:`, this.players.map(p => `${p.nickname} (${p.id})`).join(', '));
    return player;
  }

  removePlayer(id) {
    const player = this.players.find(p => p.id === id);
    if (player) {
      console.log(`[GameState] Removing player: ${player.nickname} (${id})`);
    }
    this.players = this.players.filter(p => p.id !== id);
    console.log(`[GameState] Current players:`, this.players.map(p => `${p.nickname} (${p.id})`).join(', '));
  }

  handleAction(id, action) {
    const player = this.players.find(p => p.id === id);
    console.log(`[DEBUG] handleAction: id=${id}, action=${JSON.stringify(action)}, phase=${this.phase}, player=${player ? player.nickname : 'null'}`);
    if (!player || !player.alive || this.phase !== 'game') return;
    if (action.type === 'move') {
      let { row, col } = player;
      switch (action.dir) {
        case 'up': row -= player.speed; break;
        case 'down': row += player.speed; break;
        case 'left': col -= player.speed; break;
        case 'right': col += player.speed; break;
      }
      if (row < 0 || row >= MAP_HEIGHT || col < 0 || col >= MAP_WIDTH) return;
      // Check walkable
      if (this.map[row][col] !== 'E') return;
      // Prevent walking onto a bomb
      if (this.bombs.some(b => !b.exploded && b.row === row && b.col === col)) return;
      player.row = row;
      player.col = col;
      // Powerup pickup
      this.powerups.forEach(powerup => {
        if (!powerup.collected && powerup.row === row && powerup.col === col) {
          switch (powerup.type) {
            case 'bomb_up': player.bombsCount++; break;
            case 'flame_up': player.flameRange++; break;
            case 'speed_up': player.speed++; break;
          }
          powerup.collected = true;
        }
      });
    } else if (action.type === 'bomb') {
      const activeBombs = this.bombs.filter(b => b.ownerId === player.id && !b.exploded);
      if (activeBombs.length >= player.bombsCount) return;
      if (this.bombs.some(b => b.row === player.row && b.col === player.col && !b.exploded)) return;
      this.bombs.push(new Bomb(player.row, player.col, player.id, player.flameRange));
    }
  }

  handleChat(id, text) {
    const player = this.players.find(p => p.id === id);
    if (!player) return;
    this.chat.push({ nickname: player.nickname, text });
    if (this.chat.length > 50) this.chat.shift();
  }

  update() {
    const now = Date.now();
    // Lobby logic
    if (this.phase === 'lobby') {
      // If all players left, reset lobby timer
      if (this.players.length === 0) {
        this.lobbyStart = now;
        return;
      }
      // If player count goes from 1 to 2, reset lobbyStart
      if (this._lastPlayerCount !== undefined && this._lastPlayerCount === 1 && this.players.length === 2) {
        this.lobbyStart = now;
      }
      this._lastPlayerCount = this.players.length;
      // Only start game if at least 2 players and 10s passed, or 4 players
      if ((this.players.length >= 2 && (now - this.lobbyStart > 10000)) || this.players.length === 4) {
        this.phase = 'game';
        this.lastUpdate = now;
        this.map = generateMap();
        this.bombs = [];
        this.explosions = [];
        this.powerups = [];
        this.winner = null;
        // Reset player positions and lives
        const positions = [...SPAWN_POSITIONS];
        for (let i = 0; i < this.players.length; i++) {
          const p = this.players[i];
          const pos = positions[i % positions.length];
          p.row = pos.row;
          p.col = pos.col;
          p.lives = 3;
          p.speed = 1;
          p.bombsCount = 1;
          p.flameRange = 1;
          p.alive = true;
        }
      }
      return;
    }
    // Game logic
    // Update bombs
    this.bombs.forEach(bomb => {
      if (!bomb.exploded && now - bomb.startTime >= bomb.timer) {
        this.handleExplosion(bomb);
      }
    });
    // Remove expired explosions
    this.explosions = this.explosions.filter(ex => ex.expiresAt > now);
    // Win condition
    const alive = this.players.filter(p => p.alive);
    if (alive.length === 1 && !this.winner) {
      this.winner = alive[0].nickname;
      this.phase = 'end';
    }
  }

  handleExplosion(bomb) {
    const affected = [{ row: bomb.row, col: bomb.col }];
    const directions = [
      { dr: -1, dc: 0 },
      { dr: 1, dc: 0 },
      { dr: 0, dc: -1 },
      { dr: 0, dc: 1 },
    ];
    for (const { dr, dc } of directions) {
      for (let i = 1; i <= bomb.flameRange; i++) {
        const r = bomb.row + dr * i;
        const c = bomb.col + dc * i;
        if (r < 0 || r >= MAP_HEIGHT || c < 0 || c >= MAP_WIDTH) break;
        if (this.map[r][c] === 'W') break;
        affected.push({ row: r, col: c });
        if (this.map[r][c] === 'B') break;
      }
    }
    const now = Date.now();
    affected.forEach(({ row, col }) => {
      this.explosions.push({ row, col, expiresAt: now + 500 });
    });
    // Destroy blocks, maybe spawn powerups
    affected.forEach(({ row, col }) => {
      if (this.map[row][col] === 'B') {
        this.map[row][col] = 'E';
        if (Math.random() < 0.3) {
          this.powerups.push(new PowerUp(row, col, randomPowerupType()));
        }
      }
    });
    // Damage players
    this.players.forEach(player => {
      if (!player.alive) return;
      if (affected.some(a => a.row === player.row && a.col === player.col)) {
        player.lives--;
        if (player.lives <= 0) player.alive = false;
      }
    });
    // Chain reaction
    this.bombs.forEach(b => {
      if (!b.exploded && affected.some(a => a.row === b.row && a.col === b.col)) {
        b.exploded = true;
        this.handleExplosion(b);
      }
    });
    bomb.exploded = true;
  }

  getState() {
    let lobbyCountdown = 0;
    if (this.phase === 'lobby') {
      lobbyCountdown = Math.max(0, 10 - Math.floor((Date.now() - this.lobbyStart) / 1000));
    }
    return {
      map: this.map,
      players: this.players.map(p => ({
        id: p.id,
        nickname: p.nickname,
        row: p.row,
        col: p.col,
        lives: p.lives,
        speed: p.speed,
        bombsCount: p.bombsCount,
        flameRange: p.flameRange,
        alive: p.alive,
      })),
      bombs: this.bombs.map(b => ({
        row: b.row,
        col: b.col,
        ownerId: b.ownerId,
        flameRange: b.flameRange,
        exploded: b.exploded,
      })),
      explosions: this.explosions.map(e => ({ row: e.row, col: e.col })),
      powerups: this.powerups.map(p => ({
        row: p.row,
        col: p.col,
        type: p.type,
        collected: p.collected,
      })),
      chat: this.chat,
      phase: this.phase,
      winner: this.winner,
      lobbyCountdown,
    };
  }
}

module.exports = GameState;
