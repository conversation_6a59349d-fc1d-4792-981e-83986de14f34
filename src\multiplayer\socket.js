// socket.js
// Simple WebSocket client for Bomberman

let socket = null;
let listeners = [];
let playerId = localStorage.getItem('playerId') || null;

export function connect(url) {
  socket = new WebSocket(url);
  socket.onopen = () => {
    console.log('[Socket] Connected to server');
  };
  socket.onmessage = (event) => {
    const msg = JSON.parse(event.data);
    if (msg.type === 'playerId' && msg.id) {
      playerId = msg.id;
      localStorage.setItem('playerId', playerId);
    }
    listeners.forEach(fn => fn(msg));
  };
  socket.onclose = () => {
    // Removed auto-reconnect to prevent flickering
    // setTimeout(() => connect(url), 2000);
  };
}

export function send(msg) {
  console.log('[Socket] send:', msg);
  if (socket && socket.readyState === WebSocket.OPEN) {
    if (msg.type === 'nickname') {
      msg.playerId = playerId;
    }
    socket.send(JSON.stringify(msg));
  } else {
    console.warn('[Socket] Tried to send but socket not open:', msg);
  }
}

export function onMessage(fn) {
  listeners.push(fn);
}

export function isConnected() {
  return socket && socket.readyState === WebSocket.OPEN;
}

export function getPlayerId() {
  return playerId;
}
